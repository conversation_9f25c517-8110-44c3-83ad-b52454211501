import { ListenID } from "ListenID";
import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { Time } from "Time";
import { RecordVo } from "RecordVo";
import { AlertManager } from "AlertManager";
import ItemModel from "ItemModel";

interface SubscribeItem {
    id: number;
    buyDay: number;
    getRewardDay: string;
    getRewardNum: number;
}

class ShopRecordData extends RecordVo.Data {
    orderList: { [key: number]: number } = {};
    subscribeList: SubscribeItem[] = [];
}

class ShopModel extends MVC.BaseModel {
    private static _instance: ShopModel = null;
    public onPayFinish: { [key: number]: Function } = {};
    public payList: any[];
    public recordVo: RecordVo.Mgr<ShopRecordData>;

    constructor() {
        super();
        if (ShopModel._instance == null) {
            ShopModel._instance = this;
        }
    }

    reset(): void {}

    static get instance(): ShopModel {
        if (ShopModel._instance == null) {
            ShopModel._instance = new ShopModel();
        }
        return ShopModel._instance;
    }

    get rVo(): ShopRecordData {
        return this.recordVo.vo;
    }

    loginFinish(): void {
        this.recordVo = new RecordVo.Mgr("Shop", () => {
            return new ShopRecordData();
        }, {
            isAutoSave: true,
            isAutoToform: false
        });
    }

    Buy(product: any, callback?: (success: boolean) => void): void {
        if (!(wonderSdk.isNative || wonderSdk.isWeChat || wonderSdk.isByteDance)) {
            return this.buyCharge(product, callback);
        }

        if (callback) {
            this.onPayFinish[product.id] = null;
            this.onPayFinish[product.id] = callback;
        }

        Notifier.send(ListenID.Pay_ToPay, product, (result: number) => {
            console.log(result);
            if (result === 1) {
                this.buyCharge(product);
            }
            if (callback) {
                callback(result === 1);
            }
        });
    }

    buyCharge(product: any, callback?: (success: boolean) => void): void {
        cc.log("[buyCharge]购买成功标记", product);
        if (this.rVo.orderList[product.id] == null) {
            this.rVo.orderList[product.id] = 0;
        }
        this.rVo.orderList[product.id]++;

        if (product.type === 2) {
            this.rVo.subscribeList.push({
                id: product.id,
                buyDay: Time.serverTimeMs,
                getRewardDay: "",
                getRewardNum: 0
            });
        }

        this.recordVo.SaveData();
        Notifier.send(ListenID.Shop_InfoUpDate);
        Notifier.send(ListenID.Shop_BuyCharge, product);
        this.onBuyChange(product);
    }

    onBuyChange(product: any): void {
        cc.log("[onBuyChange]", product);
        const reward = ItemModel.instance.arrToReward(product.reward[0]);
        Notifier.send(ListenID.Item_GetReward, reward);
        AlertManager.showNormalTipsOnce("购买成功");
        Notifier.send(ListenID.Refresh_Item);
    }

    get shopInfo(): number {
        return 1;
    }

    getFromPrice(product: any): any {
        const payItem = this.payList?.find(item => item.product === product.productId);
        return payItem?.formPrice || product.buyValue;
    }

    checkSubscribeCanGet(product: any): boolean {
        const currentDay = new Date(Time.serverTimeMs).getMonthDay();
        const subscribeItem = this.rVo.subscribeList.find(item => item.id === product.id);
        return !!subscribeItem && subscribeItem.getRewardDay !== currentDay;
    }

    checkOrder(productId: number): number {
        return this.rVo.orderList[productId] || 0;
    }

    get Subscrib_30(): SubscribeItem | undefined {
        return this.rVo.subscribeList.find(item => item.id === 200);
    }

    get Subscrib_Long(): SubscribeItem | undefined {
        return this.rVo.subscribeList.find(item => item.id === 201);
    }
}

export default ShopModel;
