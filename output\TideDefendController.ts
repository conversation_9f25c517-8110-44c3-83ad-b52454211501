import { CallID } from "CallID";
import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { NotifyID } from "NotifyID";
import { ListenID } from "ListenID";
import { Time } from "Time";
import { UIManager } from "UIManager";
import { NodePool } from "NodePool";
import TideDefendModel from "TideDefendModel";

export class TideDefendController extends MVC.MController {
    private oldArgs: any;

    constructor() {
        super();
        this.setup(TideDefendModel.instance);
        this.changeListener(true);
    }

    reset(): void {
        this._model.reset();
    }

    get classname(): string {
        return "TideDefendController";
    }

    registerAllProtocol(): void {}

    changeListener(enable: boolean): void {
        Notifier.changeListener(enable, ListenID.Game_Load, this.onOpenGame, this);
        Notifier.changeListener(enable, ListenID.Game_Replay, this.onReplay, this);
        Notifier.changeListener(enable, ListenID.Game_NextLV, this.onNextLV, this);
        Notifier.changeListener(enable, ListenID.Login_Finish, this.loginFinish, this);
        Notifier.changeListener(enable, ListenID.Fight_BackToMain, this.backToMain, this);
    }

    closeGame(): void {
        Notifier.send(ListenID.Fight_SetPause, true);
        UIManager.Close("ui/ModeTideDefend/M34_FightUIView");
        UIManager.Close("ui/ModeTideDefend/M34_FightScene");
    }

    setRedordTime(time: number = 1): void {
        this.setNewRedordTime(time);
    }

    setNewRedordTime(time: number = 1): void {
        const miniGameCfg = this.mode.miniGameCfg;
        let gameId: number;
        if (miniGameCfg) {
            gameId = miniGameCfg.id;
        }
        
        if (gameId) {
            if (time < 0) {
                this.mode.setTideRecordFreshTime(time, gameId);
            } else {
                this.mode.setTideRecordFreshTime(new Date().getTime(), gameId);
            }
            this.mode.recordVo.SaveData();
        }
    }

    backToMain(): void {
        if (this._model.gameMode === this.cutMode) {
            this.setRedordTime();
            this.closeGame();
            NodePool.clear();
        }
    }

    get cutMode(): any {
        return Notifier.call(CallID.Fight_GetCutMode);
    }

    onOpenGame(gameMode: any, args: any): void {
        if (this._model.gameMode === gameMode) {
            this.oldArgs = args;
            args.setIsNeedLoading(false).setNodeGroup(0);
            UIManager.Open("ui/ModeTideDefend/M34_FightScene", args);
        }
    }

    loginFinish(): void {
        this.mode.loginFinish();
    }

    onNextLV(args: any): void {
        if (this._model.gameMode === this.cutMode) {
            this.closeGame();
            this.setRedordTime();
            NodePool.clear();
            Notifier.send(NotifyID.Game_LoadingView, true);
            Time.delay(1, () => {
                Notifier.send(NotifyID.Game_LoadingView, false);
                Notifier.send(ListenID.Game_Load, this.mode.gameMode, args);
            });
        }
    }

    onReplay(): void {
        if (this._model.gameMode === this.cutMode) {
            this.closeGame();
            this.setRedordTime(-1);
            Notifier.send(NotifyID.Game_LoadingView, true);
            Time.delay(1, () => {
                Notifier.send(NotifyID.Game_LoadingView, false);
                Notifier.send(ListenID.Game_Load, this.mode.gameMode, this.oldArgs);
            });
        }
    }
}
