import { Call<PERSON> } from "CallID";
import { Cfg } from "Cfg";
import { MVC } from "MVC";
import { PoolManager } from "Pool";
import { Notifier } from "Notifier";
import { Manager } from "Manager";
import { Time } from "Time";
import { UIManager } from "UIManager";
import { GameUtil } from "GameUtil";
import FColliderManager from "FColliderManager";
import ModeBackpackHeroModel from "ModeBackpackHeroModel";
import { AlertManager } from "AlertManager";
import GoodsUIItem from "GoodsUIItem";
import RBadgeModel from "RBadgeModel";
import { Game } from "Game";
import CompManager from "CompManager";
import { Hurt } from "PropertyVo";
import { NodePool } from "NodePool";
import SettingModel from "SettingModel";
import TaskModel from "TaskModel";

class TestModel extends MVC.BaseModel {
    private static _instance: TestModel = null;

    constructor() {
        super();
        if (TestModel._instance == null) {
            TestModel._instance = this;
        }
        (window as any).W = this;
    }

    reset(): void {}

    get role(): any {
        return Game.Mgr.instance.mainRole;
    }

    get entityNode(): any {
        return Game.Mgr.instance?._entityNode;
    }

    get Boss(): any {
        return Notifier.call(CallID.Fight_GetBoss);
    }

    get Notifier(): any {
        return Notifier;
    }

    get GameUtil(): any {
        return GameUtil;
    }

    get SwitchVo(): any {
        return Manager.vo.switchVo;
    }

    get CompManager(): any {
        return CompManager.Instance;
    }

    get vo(): any {
        return Manager.vo;
    }

    get Game(): any {
        return Game.mgr;
    }

    get NodePool(): any {
        return NodePool;
    }

    get Audio(): any {
        return Manager.audio;
    }

    get PoolManager(): any {
        return PoolManager;
    }

    get ModeBackpackHeroModel(): any {
        return ModeBackpackHeroModel.instance;
    }

    get UIManager(): any {
        return UIManager.instance;
    }

    get AlertManager(): any {
        return AlertManager;
    }

    get Cfg(): any {
        return Cfg;
    }

    get Time(): any {
        return Time;
    }

    get GoodsUI(): any {
        return GoodsUIItem.List;
    }

    get Manager(): any {
        return Manager;
    }

    get Storage(): any {
        return Manager.storage;
    }

    get Scenc(): cc.Scene {
        return cc.director.getScene();
    }

    get SettingModel(): any {
        return SettingModel.instance;
    }

    get Hurt(): any {
        return Hurt;
    }

    get FColliderManager(): any {
        return FColliderManager.instance;
    }

    get TaskModel(): any {
        return TaskModel.instance;
    }

    get MBack(): any {
        return ModeBackpackHeroModel.instance;
    }

    get RBadgeModel(): any {
        return RBadgeModel.instance;
    }

    static get instance(): TestModel {
        if (TestModel._instance == null) {
            TestModel._instance = new TestModel();
        }
        return TestModel._instance;
    }
}

export default TestModel;
