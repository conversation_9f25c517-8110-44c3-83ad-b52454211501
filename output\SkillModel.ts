import { ListenID } from "ListenID";
import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { GameUtil } from "GameUtil";
import { Game, ModeCfg } from "Game";

interface SkillPool {
    free: number[];
    video: number[];
    max: number[];
}

interface SkillWeight {
    id: number;
    lv: number;
    w: number;
}

class SkillModel extends MVC.BaseModel {
    private static _instance: SkillModel = null;
    private _cutMaxSkillNum: number = 0;
    private _cutMaxSkillID: number = 0;
    private _cutLevelSkill: any;
    private config: any;

    constructor() {
        super();
        if (SkillModel._instance == null) {
            SkillModel._instance = this;
        }
        this.changeListener(true);
    }

    static get getInstance(): SkillModel {
        if (SkillModel._instance == null) {
            SkillModel._instance = new SkillModel();
        }
        return SkillModel._instance;
    }

    reset(): void {}

    changeListener(enable: boolean): void {
        Notifier.changeListener(enable, ListenID.Fight_AddSkill, this.addSkill, this);
        Notifier.changeListener(enable, ListenID.Game_Load, this.onOpenGame, this, Notifier.PriorLowest);
    }

    onOpenGame(): void {
        this.config = ModeCfg.Skill.getAll();
    }

    addSkill(skillId: number): void {
        if (skillId === this._cutMaxSkillID) {
            this._cutMaxSkillNum++;
        }
    }

    get role(): any {
        return Game.mgr.mainRole;
    }

    get game(): any {
        return Game.mgr;
    }

    get cutLevelSkill(): any {
        return this._cutLevelSkill;
    }

    set cutLevelSkill(value: any) {
        this._cutLevelSkill = value;
        this._cutMaxSkillNum = 0;
    }

    getSkillPool(skillIds: number[]): SkillPool {
        const config = this.config;
        const currentSkillIds: number[] = [];
        
        this.role.skillMgr.skills.forEach((skill: any) => {
            currentSkillIds.push(skill.id);
        });

        const getAvailableSkills = (): number[] => {
            const available: number[] = [];
            if (this.role.skillMgr.isPoolFull) {
                return available;
            }
            
            skillIds.forEach((skillId: number) => {
                for (const currentId of currentSkillIds) {
                    if (this.getMianID(currentId) === skillId) {
                        return;
                    }
                }
                available.push(skillId);
            });
            
            return available;
        };

        const freeSkills = [...getAvailableSkills()];
        const videoSkills: number[] = [];
        const maxSkills: number[] = [];

        const processSkill = (skillId: number, free: number[] = [], video: number[] = [], max: number[] = []) => {
            const nextId = skillId + 1;
            if (config[nextId]) {
                if (config[nextId].isAdGet) {
                    video.push(nextId);
                } else {
                    free.push(nextId);
                }
                const maxLvId = this.getMaxLvID(skillId);
                if (nextId !== maxLvId && config[maxLvId].isAdGet) {
                    max.push(maxLvId);
                }
            }
        };

        currentSkillIds.forEach((skillId: number) => {
            processSkill(skillId, freeSkills, videoSkills, maxSkills);
        });

        return {
            free: freeSkills,
            video: videoSkills,
            max: maxSkills
        };
    }

    randomSkill(skillPool: SkillPool, count: number = 3, maxChance: number = 40): any[] {
        let maxSkillId: number;
        const videoCount = Math.floor(count / 2);
        const freeSkills = [...skillPool.free];
        const freeCount = count - videoCount;
        const videoSkills = [...skillPool.video];
        const maxSkills = [...skillPool.max];

        if (maxSkills.length > 0 && this._cutMaxSkillNum < this._cutLevelSkill.maxLimit && 
            Game.random(1, 100) < maxChance) {
            maxSkillId = GameUtil.getRandomInArray(maxSkills, 1)[0];
        }
        this._cutMaxSkillID = maxSkillId;

        const getVideoSkills = (): number[] => {
            const selected: number[] = [];
            if (maxSkillId) {
                selected.push(maxSkillId);
            }
            
            const tempVideoSkills = [...videoSkills];
            while (selected.length < freeCount && tempVideoSkills.length > 0) {
                const randomIndex = Game.random(0, tempVideoSkills.length - 1);
                if (this.checkMainIDInArr(tempVideoSkills[randomIndex], selected)) {
                    tempVideoSkills.splice(randomIndex, 1);
                } else {
                    selected.push(tempVideoSkills.splice(randomIndex, 1)[0]);
                }
            }
            return selected;
        };

        const selectedVideoSkills = getVideoSkills();

        const getFreeSkills = (): number[] => {
            const selected: number[] = [];
            while (selected.length + selectedVideoSkills.length < count && freeSkills.length > 0) {
                selected.push(freeSkills.splice(Game.random(0, freeSkills.length - 1), 1)[0]);
            }
            return selected;
        };

        const selectedFreeSkills = getFreeSkills();

        const getAdditionalVideoSkills = (): number[] => {
            const selected: number[] = [];
            if (selectedFreeSkills.length !== 0) {
                return selected;
            }
            
            const sortedVideoSkills = this.getLvSort(videoSkills);
            while (selected.length + selectedVideoSkills.length < count && sortedVideoSkills.length > 0) {
                const weightedSkill = GameUtil.weightGetValue(sortedVideoSkills);
                if (!this.checkMainIDInArr(weightedSkill.id, selectedVideoSkills)) {
                    selected.push(weightedSkill.id);
                }
                sortedVideoSkills.splice(sortedVideoSkills.indexOf(weightedSkill), 1);
            }
            return selected;
        };

        let additionalVideoSkills = getAdditionalVideoSkills();

        if (selectedFreeSkills.length === 0 && additionalVideoSkills.length === 0 && selectedVideoSkills.length >= 2) {
            const sortedVideoSkills = this.getLvSort(selectedVideoSkills);
            additionalVideoSkills.push(selectedVideoSkills.splice(selectedVideoSkills.indexOf(sortedVideoSkills[0].id), 1)[0]);
        }

        const result: any[] = [];
        
        selectedFreeSkills.forEach((skillId: number) => {
            result.push(ModeCfg.Skill.get(skillId));
        });

        additionalVideoSkills.forEach((skillId: number) => {
            const skill = ModeCfg.Skill.get(skillId);
            skill.isAdGet = 0;
            result.push(skill);
        });

        selectedVideoSkills.forEach((skillId: number) => {
            result.push(ModeCfg.Skill.get(skillId));
        });

        return result.splice(0, count);
    }

    getLvSort(skillIds: number[]): SkillWeight[] {
        const weights: SkillWeight[] = [];
        skillIds.forEach((skillId: number) => {
            const level = this.getSkillLv(skillId);
            weights.push({
                id: skillId,
                lv: level,
                w: 100 * (40 - level * level)
            });
        });

        weights.sort((a, b) => a.lv - b.lv);
        return weights;
    }

    checkMainIDInArr(skillId: number, skillIds: number[]): boolean {
        for (let i = 0; i < skillIds.length; i++) {
            if (this.getMianID(skillIds[i]) === this.getMianID(skillId)) {
                return true;
            }
        }
        return false;
    }

    getMianID(skillId: number): number {
        return 20 * Math.floor(skillId / 20);
    }

    getSkillLv(skillId: number): number {
        return skillId % 20 + 1;
    }

    getMaxLvID(skillId: number): number {
        let maxId = this.getMianID(skillId) + 19;
        while (!this.config[maxId]) {
            maxId--;
        }
        return maxId;
    }
}

export default SkillModel;
