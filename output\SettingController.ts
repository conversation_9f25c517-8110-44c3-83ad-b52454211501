import { CallID } from "CallID";
import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { ListenID } from "ListenID";
import { Manager } from "Manager";
import { UIManager } from "UIManager";
import SettingModel from "SettingModel";

export class SettingController extends MVC.MController {
    
    constructor() {
        super();
        this.setup(SettingModel.instance);
        this.changeListener(true);
        this.onSettingInit();
    }

    reset(): void {
        this._model.reset();
    }

    get classname(): string {
        return "SettingController";
    }

    registerAllProtocol(): void {}

    changeListener(enable: boolean): void {
        Notifier.changeCall(enable, CallID.Setting_IsEnableMusic, this.isEnableMusic, this);
        Notifier.changeCall(enable, CallID.Setting_IsEnableShake, this.isEnableShake, this);
        Notifier.changeListener(enable, ListenID.Login_Finish, this.loginFinish, this);
        Notifier.changeListener(enable, ListenID.Setting_EnableMusic, this.setEnableMusic, this);
        Notifier.changeListener(enable, ListenID.Setting_EnableAudio, this.setEnableAudio, this);
        Notifier.changeListener(enable, ListenID.Setting_ValueMusic, this.setValueMusic, this);
        Notifier.changeListener(enable, ListenID.Setting_ValueAudio, this.setValueAudio, this);
        Notifier.changeListener(enable, ListenID.Setting_EnableShake, this.setEnableShake, this);
        Notifier.changeListener(enable, ListenID.Setting_OpenView, this.openSettingView, this);
        Notifier.changeListener(enable, ListenID.Activity_OpenExchangeCode, this.openExchangeCodeView, this);
    }

    loginFinish(): void {
        this._model.initSetting();
    }

    onSettingInit(): void {
        Manager.vo.designSize = this._model.getRealDesignSize();
    }

    isEnableMusic(): boolean {
        return this._model.toggle.music;
    }

    isEnableShake(): boolean {
        return this._model.toggle.shake;
    }

    setEnableAudio(enable: boolean): void {
        this._model.toggle.audio = enable;
        Manager.audio.setEnableAudio(enable);
        this.mode.save();
    }

    setEnableMusic(enable: boolean): void {
        this._model.toggle.music = enable;
        Manager.audio.setMusicEnable(enable);
        this.mode.save();
    }

    setValueAudio(value: number): void {
        this._model.toggle.audioValue = value;
        SettingModel.instance.toggle.audioValue = value;
        this.mode.save();
    }

    setValueMusic(value: number): void {
        this._model.toggle.musicValue = value;
        SettingModel.instance.toggle.musicValue = value;
        Manager.audio.setMusicVolume(value);
        this.mode.save();
    }

    setEnableShake(enable: boolean): void {
        this._model.toggle.shake = enable;
        this.mode.save();
    }

    openSettingView(): void {
        UIManager.Open("ui/setting/SettingView", MVC.openArgs());
    }

    onOpenUserAndPolicy(title: string, url: string): void {
        UIManager.Open("ui/setting/UserAndPolicyView", MVC.openArgs().setParam({
            title: title,
            url: url
        }));
    }

    openExchangeCodeView(isLive: boolean): void {
        UIManager.Open("ui/setting/ExchangeCodeView", MVC.openArgs().setParam({
            isLive: isLive
        }));
    }
}
