import { GameSeting } from "GameSeting";
import { ListenID } from "ListenID";
import { Notifier } from "Notifier";
import { GameUtil } from "GameUtil";
import { Game, ModeCfg } from "Game";
import { EntityType } from "BaseEntity";
import SkillModel from "SkillModel";

export namespace Skill {
    
    interface ReadySkillInfo {
        id: number;
        dis: number;
    }

    export class SkillManager {
        public poolLimt: number = 99;
        public skills: any[] = [];
        public skillIDs: number[] = [];
        public subSkills: Sub.Data[] = [];
        public launchPoint: cc.Vec2 = cc.v2(0, 80);
        public maxGap: number = 0;
        public readySkill: ReadySkillInfo[] = [];
        public onReady: () => void = () => {};
        private ower: any;
        private changeTimer: any;

        constructor(owner: any) {
            this.ower = owner;
        }

        get game(): any {
            return Game.mgr;
        }

        get model(): any {
            return SkillModel.getInstance;
        }

        get activeSkills(): any[] {
            return this.skills.filter(skill => !skill.isAuto);
        }

        add(skillId: number, isAuto: boolean = true, isStartSkill: boolean = false): any {
            const skillConfig = ModeCfg.Skill.get(skillId);
            if (skillConfig) {
                return this.addByData(skillConfig, isAuto, isStartSkill);
            }
        }

        addByData(skillConfig: any, isAuto: boolean = true, isStartSkill: boolean = false): any {
            if (!this.isPoolFull) {
                const existingSkill = this.filterMainID(this.model.getMianID(skillConfig.id));
                if (existingSkill) {
                    isStartSkill = existingSkill.isStartSkill;
                    this.clearByID(existingSkill.id);
                }

                const skillInstance = this.getSkillClass(skillConfig);
                this.skills.push(skillInstance);
                skillInstance.index = this.skills.length - 1;
                skillInstance.isAuto = isAuto;
                skillInstance.isStartSkill = isStartSkill;
                this.onSkillChange();
                return skillInstance;
            }
            cc.log("技能池已满");
        }

        getSkillClass(skillConfig: any): any {
            const SkillClass = cc.js.getClassByName(skillConfig.skillClass || "Skill_Default");
            if (SkillClass) {
                const skillInstance = new SkillClass();
                skillInstance.setOwner(this.ower);
                skillInstance.skillCfg = skillConfig;
                skillInstance.load();
                return skillInstance;
            }
        }

        get(skillId: number): any {
            return this.skills.find(skill => skill.id === skillId);
        }

        addSubSkill(skillId: number): any {
            const skillConfig = ModeCfg.Skill.get(skillId);
            if (!skillConfig) {
                cc.warn("创建技能失败", skillId);
                return null;
            }

            const mainId = this.model.getMianID(skillId);
            const existingSubSkill = this.subSkills.find(subSkill => 
                subSkill.skill.skillMainID === mainId);
            
            if (existingSubSkill) {
                this.subSkills.delete(existingSubSkill);
            }

            return this.getSkillClass(skillConfig);
        }

        useSubSkill(skillData: number[], releaseType: number, fireData: any): void {
            const mainId = this.model.getMianID(skillData[0]);
            let subSkill = this.subSkills.find(subSkill => 
                subSkill.skill.skillMainID === mainId);
            
            if (!subSkill) {
                subSkill = this.analysisData([skillData], this.ower)[0];
            }

            if (releaseType === subSkill.release) {
                subSkill.use(fireData);
            }
        }

        clearAll(): void {
            for (let i = this.skills.length - 1; i >= 0; i--) {
                this.skills[i].unload();
                this.skills.splice(i, 1);
            }
            this.skills.length = 0;
            this.onSkillChange();
        }

        clearByID(skillId: number, useMainId: boolean = false): void {
            if (skillId) {
                for (let i = 0; i < this.skills.length; i++) {
                    const skill = this.skills[i];
                    if (useMainId ? skill.skillMainID === skillId : skill.id === skillId) {
                        skill.unload();
                        this.skills.splice(i, 1);
                        break;
                    }
                }
                this.onSkillChange();
            }
        }

        use(skillId: number): boolean {
            const mainId = this.model.getMianID(skillId);
            const skill = this.skills.find(skill => skill.skillMainID === mainId);
            
            if (!skill || !skill.isReady) {
                return false;
            }
            
            skill.checkTarget();
            return true;
        }

        filterMainID(mainId: number): any {
            return this.skills.filter(skill => skill.skillMainID === mainId)[0];
        }

        hasMainID(mainId: number): boolean {
            for (let i = 0; i < this.skills.length; i++) {
                if (this.skills[i].skillMainID === mainId) {
                    return true;
                }
            }
            return false;
        }

        onSkillChange(): void {
            if (this.ower.entityType === EntityType.Role) {
                this.game.timeDelay.cancelBy(this.changeTimer);
                this.changeTimer = this.ower.delayByGame(() => {
                    if (this.skills) {
                        Notifier.send(ListenID.Fight_OnSkillChange, this.skills);
                    }
                }, 0.2).id;
            }

            this.skillIDs.length = 0;
            this.skillIDs.push(
                ...this.skills.map(skill => skill.skillCfg.id),
                ...this.subSkills.map(subSkill => subSkill.skill.skillCfg.id)
            );

            this.ower.buffMgr?.bufflist.forEach((buff: any) => {
                if (buff.cutVo.skillId) {
                    const isActive = GameUtil.hasIntersection(buff.cutVo.skillId, this.skillIDs);
                    buff._isActive = isActive;
                    if (isActive) {
                        buff.buffLayer = buff.buffLayer;
                    }
                }
            });
        }

        onUpdate(deltaTime: number): void {
            this.skills.forEach(skill => skill.onUpdate(deltaTime));
            this.subSkills.forEach(subSkill => subSkill.skill.onUpdate(deltaTime));
        }

        onBuff(buffData: any): void {
            this.skills.forReverse((skill: any) => skill.onBuff(buffData));
            this.subSkills.forReverse((subSkill: Sub.Data) => subSkill.skill.onBuff(buffData));
        }

        get isPoolFull(): boolean {
            return this.skills.length >= this.poolLimt;
        }

        getReadySkill(maxDistance: number = 10000): ReadySkillInfo[] {
            this.readySkill.length = 0;
            const readySkills: ReadySkillInfo[] = [];

            for (let i = this.skills.length - 1; i >= 0; i--) {
                const skill = this.skills[i];
                if (skill.isReady && maxDistance <= skill.cutVo.dis) {
                    readySkills.push({
                        id: skill.id,
                        dis: skill.cutVo.dis
                    });
                }
            }

            readySkills.sort((a, b) => b.dis - a.dis);
            this.readySkill = readySkills;
            this.maxGap = readySkills[0]?.dis || 0;
            return readySkills;
        }

        destroy(): void {
            this.clearAll();
        }

        analysisData(skillDataArray: number[][], owner: any): Sub.Data[] {
            if (!skillDataArray) {
                return [];
            }

            const subSkills: Sub.Data[] = [];
            skillDataArray.forEach(skillData => {
                const existingSubSkill = this.subSkills.find(subSkill => 
                    subSkill.ID === skillData[0]);
                
                if (existingSubSkill) {
                    subSkills.push(existingSubSkill);
                } else {
                    const newSubSkill = new Sub.Data(skillData, owner);
                    if (newSubSkill.skill) {
                        subSkills.push(newSubSkill);
                    }
                }
            });

            return subSkills;
        }
    }

    export namespace Sub {
        export class Data {
            public tempPos: cc.Vec2 = cc.Vec2.ZERO;
            public canFireNum: number = 0;
            public ID: number;
            public release: number;
            public spawningType: number;
            public delay: number;
            public weight: number;
            public skill: any;

            constructor(skillData: number[], owner: any) {
                this.ID = skillData[0];
                this.release = skillData[1];
                this.spawningType = skillData[2];
                this.delay = skillData[3] || 0;
                this.weight = skillData[4] || 1;
                this.skill = owner.skillMgr.addSubSkill(this.ID);
                
                if (this.skill) {
                    owner.skillMgr.subSkills.push(this);
                }
            }

            use(fireData: any): void {
                if (!this.canFireNum) return;
                
                if (this.skill._cd < this.skill.cutCDLimt || Game.weightFloat(this.weight)) {
                    if (this.spawningType === GameSeting.SpawningPos.Self) {
                        fireData.pos.set(this.skill.launchPoint);
                    } else if (this.spawningType === GameSeting.SpawningPos.JoytPos) {
                        fireData.pos.set(this.skill.joytPos.add(this.skill.launchPoint));
                    }

                    this.skill.owner.delayByGame(() => {
                        if (this.skill) {
                            this.skill.belongSkillID = fireData.belongSkillID;
                            this.skill.fire(fireData);
                            this.canFireNum--;
                        }
                    }, this.delay);

                    this.skill._cd = 0;
                }
            }
        }
    }

    export const SelfDestructSkill = [2060, 66600];
}
