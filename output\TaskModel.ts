import { ListenID } from "ListenID";
import { Cfg } from "Cfg";
import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { Time } from "Time";
import { GameUtil } from "GameUtil";
import { RBadge } from "RBadgeModel";

export enum TaskSaveType {
    SaveProgressId = "SaveProgressId",
    TaskProgressData = "TaskProgressData",
    SaveGetTaskId = "SaveGetTaskId",
    GameTaskAchieve = "GameTaskAchieve",
    GameTaskDay = "GameTaskDay",
    GameTaskWeek = "GameTaskWeek"
}

interface TaskData {
    id: number;
    type: number;
    beyongd: number;
    value: number[];
    status: number;
    pre?: boolean;
}

class TaskModel extends MVC.BaseModel {
    private static _instance: TaskModel = null;
    
    public commonDayTaskData: TaskData[] = [];
    public commonWeekTaskData: TaskData[] = [];
    public commonProgressDayTaskData: any[] = [];
    public commonProgressWeekTaskData: any[] = [];
    public achieveTaskData: Map<number, TaskData[]> = new Map();
    public achieveTaskList: TaskData[] = [];

    constructor() {
        super();
        if (TaskModel._instance == null) {
            TaskModel._instance = this;
        }
    }

    reset(): void {}

    static get instance(): TaskModel {
        if (TaskModel._instance == null) {
            TaskModel._instance = new TaskModel();
        }
        return TaskModel._instance;
    }

    initTaskData(): void {
        this.commonDayTaskData = [];
        const taskGroups: TaskData[][] = [];
        const allTasks = Cfg.Task.getAll();
        
        for (const taskId in allTasks) {
            const task = allTasks[taskId];
            if (!taskGroups[task.beyongd - 1]) {
                taskGroups[task.beyongd - 1] = [];
            }
            taskGroups[task.beyongd - 1].push(task);
        }

        // Process day tasks
        if (taskGroups[0]) {
            taskGroups[0].forEach((task: TaskData) => {
                const taskCopy = GameUtil.deepCopy(task);
                taskCopy.status = this.getGetTaskId(task.id, TaskSaveType.SaveGetTaskId) ? 0 : 
                    this.getTaskCurCount(task.type, TaskSaveType.GameTaskDay) >= task.value[0] ? 2 : 1;
                this.commonDayTaskData.push(taskCopy);
            });
        }
        this.updateTaskDayRedpoint();

        // Process week tasks
        if (taskGroups[1]) {
            taskGroups[1].forEach((task: TaskData) => {
                const taskCopy = GameUtil.deepCopy(task);
                taskCopy.status = this.getGetTaskId(task.id, TaskSaveType.SaveGetTaskId) ? 0 : 
                    this.getTaskCurCount(task.type, TaskSaveType.GameTaskWeek) >= task.value[0] ? 2 : 1;
                this.commonWeekTaskData.push(taskCopy);
            });
        }
        this.updateTaskWeekRedpoint();

        // Process achieve tasks
        this.achieveTaskList = [];
        this.achieveTaskData.clear();
        const achieveGroups: TaskData[][] = [];
        
        if (taskGroups[2]) {
            taskGroups[2].forEach((task: TaskData) => {
                if (!achieveGroups[task.type]) {
                    achieveGroups[task.type] = [];
                }
                achieveGroups[task.type].push(task);
                this.achieveTaskData.set(task.type, achieveGroups[task.type]);
            });
        }

        this.achieveTaskData.forEach((tasks: TaskData[]) => {
            for (let i = 0; i < tasks.length; i++) {
                tasks[i].status = this.getGetTaskId(tasks[i].id, TaskSaveType.SaveGetTaskId) ? 0 : 
                    this.getTaskCurCount(tasks[i].type, TaskSaveType.GameTaskAchieve) >= tasks[i].value[0] ? 2 : 1;
                if (!tasks[i].pre && tasks[i].status !== 0) {
                    this.achieveTaskList.push(tasks[i]);
                }
            }

            const activeIndex = tasks.findIndex(task => task.status !== 0);
            const targetIndex = activeIndex !== -1 ? activeIndex : tasks.length - 1;
            
            if (!this.achieveTaskList.find(task => task.id === tasks[targetIndex].id)) {
                this.achieveTaskList.push(tasks[targetIndex]);
            }
        });
        
        this.updateTaskAchieveRedpoint();
    }

    updateTaskDayRedpoint(): void {
        let hasReward = 0;
        for (let i = 0; i < this.commonDayTaskData.length; i++) {
            if (this.commonDayTaskData[i].status === 2) {
                hasReward = 1;
                break;
            }
        }
        Notifier.send(ListenID.Badge_Set, RBadge.Key.DayTaskItem, hasReward);
    }

    updateTaskWeekRedpoint(): void {
        let hasReward = 0;
        for (let i = 0; i < this.commonWeekTaskData.length; i++) {
            if (this.commonWeekTaskData[i].status === 2) {
                hasReward = 1;
                break;
            }
        }
        Notifier.send(ListenID.Badge_Set, RBadge.Key.WeekTaskItem, hasReward);
    }

    updateTaskAchieveRedpoint(): void {
        let hasReward = 0;
        for (let i = 0; i < this.achieveTaskList.length; i++) {
            if (this.achieveTaskList[i].status === 2) {
                hasReward = 1;
                break;
            }
        }
        Notifier.send(ListenID.Badge_Set, RBadge.Key.TaskAchieve, hasReward);
    }

    insertTask(taskType: number, value: number): void {
        this.setSaveTaskData(taskType, value, TaskSaveType.GameTaskDay);
        this.setSaveTaskData(taskType, value, TaskSaveType.GameTaskWeek);
        this.setSaveTaskData(taskType, value, TaskSaveType.GameTaskAchieve);

        if (this.commonDayTaskData) {
            this.commonDayTaskData.forEach((task: TaskData) => {
                task.status = this.getGetTaskId(task.id, TaskSaveType.SaveGetTaskId) ? 0 : 
                    this.getTaskCurCount(task.type, TaskSaveType.GameTaskDay) >= task.value[0] ? 2 : 1;
            });
        }
        this.updateTaskDayRedpoint();

        if (this.commonWeekTaskData) {
            this.commonWeekTaskData.forEach((task: TaskData) => {
                task.status = this.getGetTaskId(task.id, TaskSaveType.SaveGetTaskId) ? 0 : 
                    this.getTaskCurCount(task.type, TaskSaveType.GameTaskWeek) >= task.value[0] ? 2 : 1;
            });
        }
        this.updateTaskWeekRedpoint();

        if (this.achieveTaskList) {
            this.achieveTaskList.forEach((task: TaskData) => {
                task.status = this.getGetTaskId(task.id, TaskSaveType.SaveGetTaskId) ? 0 : 
                    this.getTaskCurCount(task.type, TaskSaveType.GameTaskAchieve) >= task.value[0] ? 2 : 1;
            });
        }

        if (this.achieveTaskData) {
            const achieveTasks = this.achieveTaskData.get(taskType);
            if (achieveTasks) {
                achieveTasks.forEach((task: TaskData) => {
                    task.status = this.getGetTaskId(task.id, TaskSaveType.SaveGetTaskId) ? 0 : 
                        this.getTaskCurCount(task.type, TaskSaveType.GameTaskAchieve) >= task.value[0] ? 2 : 1;
                });
            }
        }
        this.updateTaskAchieveRedpoint();
    }

    setSaveTaskId(taskId: number, saveType: TaskSaveType): void {
        const savedData = cc.sys.localStorage.getItem(saveType, []);
        let taskIds: number[] = [];
        if (savedData && savedData !== "") {
            taskIds = JSON.parse(savedData) || [];
        }
        taskIds.push(taskId);
        const dataString = JSON.stringify(taskIds);
        cc.sys.localStorage.setItem(saveType, dataString);
    }

    getGetTaskId(taskId: number, saveType: TaskSaveType): boolean {
        const savedData = cc.sys.localStorage.getItem(saveType, []);
        let taskIds: number[] = [];
        if (savedData && savedData !== "") {
            taskIds = JSON.parse(savedData) || [];
        }
        return taskIds.includes(taskId);
    }

    getTaskProgressReward(): void {
        const processRewards = Cfg.ProcessRewards.getAll();
        for (const rewardId in processRewards) {
            const reward = processRewards[rewardId];
            if (reward.beyongd === 1) {
                this.commonProgressDayTaskData.push(reward);
            } else if (reward.beyongd === 2) {
                this.commonProgressWeekTaskData.push(reward);
            }
        }

        this.commonProgressDayTaskData.sort((a, b) => a.proId - b.proId);
        this.updateTaskProgressData(0);
        
        this.commonProgressWeekTaskData.sort((a, b) => a.proId - b.proId);
        this.updateTaskProgressData(1);
    }

    updateTaskProgressData(type: number): void {
        const progressData = type === 0 ? this.commonProgressDayTaskData : this.commonProgressWeekTaskData;
        const currentCount = this.getTaskCurCount(type + 1, TaskSaveType.TaskProgressData);
        let hasReward = 0;
        
        for (let i = 0; i < 5; i++) {
            if (!this.getGetTaskId(progressData[i].proId, TaskSaveType.SaveProgressId) && 
                currentCount >= progressData[i].needNum) {
                hasReward = 1;
                break;
            }
        }
        
        Notifier.send(ListenID.Badge_Set, 
            type === 0 ? RBadge.Key.DayTaskBox : RBadge.Key.WeekTaskBox, hasReward);
    }

    setSaveTaskData(taskType: number, value: number, saveType: TaskSaveType): void {
        const savedData = cc.sys.localStorage.getItem(saveType);
        if (savedData && savedData !== "") {
            const parsedData = JSON.parse(savedData);
            const dataMap = new Map(parsedData);
            
            if (dataMap.get(taskType)) {
                if (taskType === 10) {
                    dataMap.set(taskType, value);
                } else {
                    dataMap.set(taskType, dataMap.get(taskType) + value);
                }
            } else {
                dataMap.set(taskType, value);
            }
            
            const dataString = JSON.stringify(Array.from(dataMap));
            cc.sys.localStorage.setItem(saveType, dataString);
        } else {
            const dataMap = new Map();
            dataMap.set(1, 1);
            dataMap.set(taskType, value);
            const dataString = JSON.stringify(Array.from(dataMap));
            cc.sys.localStorage.setItem(saveType, dataString);
        }
    }

    getTaskCurCount(taskType: number, saveType: TaskSaveType): number {
        const savedData = cc.sys.localStorage.getItem(saveType);
        if (savedData && savedData !== "") {
            const parsedData = JSON.parse(savedData);
            const dataMap = new Map(parsedData);
            if (dataMap.get(taskType)) {
                return dataMap.get(taskType);
            }
        }
        return 0;
    }

    updateAchieveTaskData(taskId: number, taskType: number): void {
        const achieveTask = this.achieveTaskList.find(task => task.id === taskId);
        if (achieveTask) {
            achieveTask.status = 0;
        }

        const achieveTasks = this.achieveTaskData.get(taskType);
        if (achieveTasks) {
            const task = achieveTasks.find(task => task.id === taskId);
            if (task) {
                task.status = 0;
            }

            const activeIndex = achieveTasks.findIndex(task => task.status !== 0);
            const targetIndex = activeIndex !== -1 ? activeIndex : achieveTasks.length - 1;

            const newAchieveList: TaskData[] = [];
            this.achieveTaskList.forEach((task: TaskData) => {
                if (!(task.type === achieveTasks[targetIndex].type && task.status === 0)) {
                    task.status = this.getGetTaskId(task.id, TaskSaveType.SaveGetTaskId) ? 0 :
                        this.getTaskCurCount(task.type, TaskSaveType.GameTaskAchieve) >= task.value[0] ? 2 : 1;
                    newAchieveList.push(task);
                }
            });
            newAchieveList.push(achieveTasks[targetIndex]);
            this.achieveTaskList = newAchieveList;
            Notifier.send(ListenID.Task_UpdateAchieveDate);
        }
    }

    clearTaskData(taskType: number): void {
        // Clear progress rewards
        const progressIds = cc.sys.localStorage.getItem(TaskSaveType.SaveProgressId, []);
        let progressIdList: number[] = [];
        if (progressIds && progressIds !== "") {
            progressIdList = JSON.parse(progressIds) || [];
        }

        const progressRewardIds = new Set<number>();
        const processRewards = Cfg.ProcessRewards.getAll();
        for (const rewardId in processRewards) {
            const reward = processRewards[rewardId];
            if (reward.beyongd === taskType) {
                progressRewardIds.add(reward.proId);
            }
        }

        if (progressIdList.length > 0) {
            progressIdList = progressIdList.filter(id => !progressRewardIds.has(id));
        }
        const progressDataString = JSON.stringify(progressIdList);
        cc.sys.localStorage.setItem(TaskSaveType.SaveProgressId, progressDataString);

        // Clear task progress data
        const taskProgressData = cc.sys.localStorage.getItem(TaskSaveType.TaskProgressData);
        if (taskProgressData && taskProgressData !== "") {
            const parsedData = JSON.parse(taskProgressData);
            const dataMap = new Map(parsedData);
            if (dataMap.get(taskType)) {
                dataMap.set(taskType, 0);
            }
            const dataString = JSON.stringify(Array.from(dataMap));
            cc.sys.localStorage.setItem(TaskSaveType.TaskProgressData, dataString);
        }

        // Clear completed task IDs
        const completedTaskIds = cc.sys.localStorage.getItem(TaskSaveType.SaveGetTaskId, []);
        let completedIdList: number[] = [];
        if (completedTaskIds && completedTaskIds !== "") {
            completedIdList = JSON.parse(completedTaskIds) || [];
        }

        const taskIds = new Set<number>();
        const allTasks = Cfg.Task.getAll();
        for (const taskId in allTasks) {
            const task = allTasks[taskId];
            if (task.beyongd === taskType) {
                taskIds.add(task.id);
            }
        }

        if (completedIdList.length > 0) {
            completedIdList = completedIdList.filter(id => !taskIds.has(id));
        }
        const completedDataString = JSON.stringify(completedIdList);
        cc.sys.localStorage.setItem(TaskSaveType.SaveGetTaskId, completedDataString);

        // Clear game task data
        const gameTaskType = taskType === 1 ? TaskSaveType.GameTaskDay : TaskSaveType.GameTaskWeek;
        cc.sys.localStorage.setItem(gameTaskType, "[]");
    }

    getNextDay(days: number = 1): number {
        const date = new Date(Time.serverTimeMs);
        date.setHours(0, 0, 0, 0);
        return date.getTime() + 86400000 * days;
    }

    setMultipleSort(array: any[], sortConfig: { [key: string]: number }): void {
        array.sort((a, b) => {
            for (const key in sortConfig) {
                if (sortConfig.hasOwnProperty(key)) {
                    const aValue = a[key];
                    const bValue = b[key];
                    if (aValue !== bValue) {
                        return sortConfig[key] * (aValue - bValue);
                    }
                }
            }
            return 0;
        });
    }

    calculateProgress(current: number, thresholds: number[]): number {
        const index = thresholds.findIndex((threshold, i) => {
            if (i === thresholds.length - 1) {
                return current >= threshold;
            } else {
                return current < thresholds[i + 1];
            }
        });

        if (index === -1) {
            return 0;
        }

        const currentThreshold = thresholds[index];
        const nextThreshold = index === thresholds.length - 1 ? current : thresholds[index + 1];

        return (index + (index === thresholds.length - 1 ? 1 : (current - currentThreshold) / (nextThreshold - currentThreshold))) / (thresholds.length - 1);
    }
}

export default TaskModel;
