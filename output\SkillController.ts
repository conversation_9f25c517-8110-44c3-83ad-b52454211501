import { MVC } from "MVC";
import { Game } from "Game";
import SkillModel from "SkillModel";

export class SkillController extends MVC.MController {
    
    constructor() {
        super();
        this.setup(SkillModel.getInstance);
        this.changeListener(true);
    }

    reset(): void {}

    get classname(): string {
        return "SkillController";
    }

    registerAllProtocol(): void {}

    get game(): any {
        return Game.Mgr.instance;
    }

    changeListener(): void {}
}
