import { Cfg } from "Cfg";
import { MVC } from "MVC";
import { GameSeting } from "GameSeting";
import { GameUtil } from "GameUtil";
import { RecordVo } from "RecordVo";
import { Game, ModeCfg } from "Game";
import { MTideDefendRebound } from "MTideDefendRebound";

class TideDefendRecordData extends RecordVo.Data {
    tideRecordFreshTime: number = -1;
    tideRecordFreshTime1: number = -1;
    tideRecordFreshTime2: number = -1;
}

interface BuffItem {
    id: number;
    w: number;
    isAd: number;
}

interface BuffResult {
    id: number;
    isAd: number;
}

interface FightBuffOptions {
    num?: number;
    isBinding?: boolean;
}

class TideDefendModel extends MVC.BaseModel {
    private static _instance: TideDefendModel = null;
    
    public gameMode: number = Game.Mode.TIDEDEFEND;
    public recordVo: RecordVo.Mgr<TideDefendRecordData> = null;
    public gmDiff: number = 0;
    private _dragonList: { [key: number]: { [key: number]: any } } = {};
    public miniGameCfg: any;
    
    private poolMap: { [key: number]: () => any } = {
        [MTideDefendRebound.poolType.NormalBuff]: () => this.cardPool.norBuffPool,
        [MTideDefendRebound.poolType.HighBuff]: () => this.cardPool.adBuffPool
    };

    constructor() {
        super();
        if (TideDefendModel._instance == null) {
            TideDefendModel._instance = this;
        }
    }

    get game(): any {
        return Game.mgr;
    }

    get role(): any {
        return this.game.mainRole;
    }

    get rVo(): TideDefendRecordData {
        return this.recordVo.vo;
    }

    reset(): void {}

    static get instance(): TideDefendModel {
        if (TideDefendModel._instance == null) {
            TideDefendModel._instance = new TideDefendModel();
        }
        return TideDefendModel._instance;
    }

    loginFinish(): void {
        this.recordVo = new RecordVo.Mgr("TideDefend", () => {
            return new TideDefendRecordData();
        });
    }

    bShowAd(gameId: number): boolean {
        return this.bShowAdNew(gameId);
    }

    bShowAdNew(gameId: number): boolean {
        let recordTime = 0;
        if (gameId === 4001) {
            recordTime = this.rVo.tideRecordFreshTime1;
        } else if (gameId === 4002) {
            recordTime = this.rVo.tideRecordFreshTime2;
        }

        const recordTimeSeconds = Math.floor(recordTime / 1000);
        const currentTimeSeconds = Math.floor(new Date().getTime() / 1000);
        const timeDiff = currentTimeSeconds - recordTimeSeconds;
        
        return timeDiff > 0 && timeDiff < 1800;
    }

    setTideRecordFreshTime(time: number, gameId: number): void {
        if (gameId === 4001) {
            this.rVo.tideRecordFreshTime1 = time;
        } else if (gameId === 4002) {
            this.rVo.tideRecordFreshTime2 = time;
        }
    }

    getTideRecordFreshTime(gameId: number): number | undefined {
        if (gameId === 4001) {
            return this.rVo.tideRecordFreshTime1;
        } else if (gameId === 4002) {
            return this.rVo.tideRecordFreshTime2;
        }
        return undefined;
    }

    getksd(): void {
        this.miniGameCfg.id;
    }

    bShowMatrix(config: any): boolean {
        return config.id === 4002;
    }

    bShowMonsterBlob(): boolean {
        return true;
    }

    bDragonBoss(config: any): boolean {
        return !!(config.monId && config.monId[0] && config.monId[0] === 999999910);
    }

    get dragonList(): { [key: number]: { [key: number]: any } } {
        if (Object.keys(this._dragonList).length === 0) {
            Cfg.bagMonsterLv.forEach((monster: any) => {
                if (monster.lv === 999999991 || monster.lv === 999999992 || 
                    monster.lv === 999999993 || monster.lv === 999999910) {
                    if (!this._dragonList[monster.lv]) {
                        this._dragonList[monster.lv] = {};
                    }
                    this._dragonList[monster.lv][monster.id] = monster;
                }
            });
        }
        return this._dragonList;
    }

    get cardPool(): any {
        const pool = Cfg.BagModeSkillPool.findBy((pool: any) => {
            return pool.modeType === this.gameMode && 
                (!this.game.miniGameCfg || pool.lv?.includes(this.game.miniGameCfg.id));
        });
        
        return pool || Cfg.BagModeSkillPool.findBy((pool: any) => {
            return pool.modeType === this.gameMode;
        });
    }

    fightBuffWidth(poolType: number, options: FightBuffOptions = {}, pool?: any): BuffResult[] {
        if (!pool) {
            pool = this.poolMap[poolType]();
        }

        if (!options.num) {
            options.num = 3;
        }
        if (options.isBinding == null) {
            options.isBinding = true;
        }

        const buffItems: BuffItem[] = [];
        const buffIds = pool[0];
        const weights = pool[1];

        this.role.buffMgr.use(3042, false, () => {
            options.num += 1;
        });

        const skillBuffIds: number[] = [];
        
        buffIds.forEach((buffId: number, index: number) => {
            const buffConfig = ModeCfg.Buff.get(buffId);
            if (!buffConfig) return;

            const existingBuff = this.role.buffMgr.get(buffId);
            
            if (buffConfig.skillId && (!options.isBinding || 
                !GameUtil.hasIntersection(this.role.skillMgr.skillIDs, buffConfig.skillId))) {
                return;
            }

            if (existingBuff) {
                if (existingBuff.isMaxLayer) return;
                if (buffConfig.isSelect === 1) return;
            }

            if (buffConfig.skillId) {
                skillBuffIds.push(buffId);
            }

            const buffItem: BuffItem = {
                id: buffId,
                w: weights[index],
                isAd: 0
            };

            this.role.buffMgr.use(3043, true, (buff: any) => {
                const rarityTypes = [GameSeting.RarityType.B, GameSeting.RarityType.A, GameSeting.RarityType.S];
                if (rarityTypes.includes(buffConfig.rarity)) {
                    buffItem.w += buff.cutVo.value[0][0];
                }
            });

            buffItems.push(buffItem);
        });

        // Remove items with weight <= 0
        for (let i = buffItems.length - 1; i >= 0; i--) {
            if (buffItems[i].w <= 0) {
                GameUtil.deleteArrItem(buffItems, buffItems[i]);
            }
        }

        const results: BuffResult[] = [];
        GameUtil.weightGetList(buffItems, options.num).forEach((item: BuffItem) => {
            results.push({
                id: item.id,
                isAd: item.isAd
            });
        });

        return results;
    }
}

export default TideDefendModel;
