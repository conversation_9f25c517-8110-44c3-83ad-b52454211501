import { TConfig } from "TConfig";

export enum SoundDefine {
    bgm_bulletrebound = 99,
    bgm_dragon = 100,
    bgm_lobby = 101,
    bgm_battle = 102,
    bgm_boss = 103,
    button_click = 104,
    reward = 105,
    skill_zj = 106,
    skill_sdl = 107,
    skill_lj = 108,
    skill_ys = 109,
    skill_jf = 110,
    skill_bb = 111,
    skill_hbz = 112,
    skill_dcj = 113,
    skill_bjsf = 114,
    boss_appear = 115,
    lvup = 116,
    box_drop = 117,
    passskill_select21 = 118,
    passskill_select52 = 119,
    roletry = 120,
    pettry = 121,
    gamewin = 122,
    gamerevive = 123,
    gamefail = 124,
    screw_in = 125,
    screw_out = 126,
    win = 127,
    weather_sunny = 128,
    weahter_rain = 129,
    weather_thunder = 130,
    weather_tinnywinter = 131,
    weather_winter = 132,
    weather_darknight = 133,
    sfx_skill_dcj2 = 134,
    sfx_skill_blast = 135,
    skill_hq = 136,
    skill_hqhit = 137,
    sfx_arrow_shoot = 1000,
    sfx_fire_boom = 1001,
    sfx_fire_shot = 1002,
    sfx_laser_shoot = 1003,
    sfx_hxb_shoot = 1004,
    sfx_ft_shoot = 1005,
    sfx_slj_shoot = 1006,
    sfx_ljf = 1007,
    sfx_tachi = 1008,
    sfx_bulletreound = 1009,
    sfx_knifereound = 1010,
    sfx_pop_1 = 1011,
    sfx_pop_2 = 1012,
    sfx_dragonappear = 1013,
    sfx_dragonhit = 1014,
    sfx_qjcj_bgm = 1015,
    sfx_qjcj_pro = 1016,
    ui_collectsilverCoin = 2000,
    ui_purchase_success = 2001,
    ui_receive_reward = 2002,
    ui_equip = 2003,
    ui_mergelv2 = 2004,
    ui_mergelv3 = 2005,
    ui_mergelv4 = 2006,
    ui_addgem = 2007,
    sfx_skill_laser = 3001,
    sfx_skill_thunderbolt = 3002,
    sfx_skill_stone = 3003,
    sfx_skill_hose = 3004,
    sfx_skill_flaming = 3005,
    sfx_skill_loong = 3006
}

export class SoundCfgReader extends TConfig {
    protected _name: string = "Sound";

    constructor() {
        super();
    }
}
