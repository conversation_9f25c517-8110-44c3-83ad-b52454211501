import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { ListenID } from "ListenID";
import { UIManager } from "UIManager";
import { Game } from "Game";
import TestModel from "TestModel";

export class TestController extends MVC.MController {
    
    constructor() {
        super();
        this.setup(TestModel.instance);
        this.changeListener(true);
    }

    get game(): any {
        return Game.mgr;
    }

    reset(): void {
        this._model.reset();
    }

    get classname(): string {
        return "TestController";
    }

    registerAllProtocol(): void {}

    changeListener(enable: boolean): void {
        Notifier.changeListener(enable, ListenID.Test_OpenView, this.onOpenView, this);
        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onkeyDown, this);
    }

    onOpenView(): void {
        UIManager.Open("ui/test/TestView", MVC.openArgs());
    }

    onkeyDown(event: cc.Event.EventKeyboard): void {
        switch (event.keyCode) {
            case cc.macro.KEY.g:
                this.onOpenView();
                break;
            case cc.macro.KEY.p:
                if (UIManager.getView("ui/common/PauseView")) {
                    UIManager.Close("ui/common/PauseView");
                } else {
                    Notifier.send(ListenID.Fight_SetPause, true);
                    UIManager.Open("ui/common/PauseView", MVC.openArgs());
                }
                break;
            case cc.macro.KEY.f1:
                const item = this.mode.Game.packView.propList.getFirst();
                UIManager.Open("ui/ModeBackpackHero/M20_Fight_Turntable", MVC.openArgs().setParam(item));
                break;
            case cc.macro.KEY["+"]:
                this.game?.gameCamera.setZoomRatio(this.game.gameCamera.cutZoomRatio + 0.1, 0.1);
                break;
            case cc.macro.KEY["-"]:
                this.game?.gameCamera.setZoomRatio(this.game.gameCamera.cutZoomRatio - 0.1, 0.1);
                break;
        }
    }
}
