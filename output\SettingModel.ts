import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { ListenID } from "ListenID";
import { Manager } from "Manager";
import { StorageID } from "StorageID";
import { GameUtil } from "GameUtil";

interface ToggleSettings {
    music: boolean;
    audio: boolean;
    shake: boolean;
    friend: boolean;
    power: boolean;
    dub: boolean;
    hurtTips: boolean;
    musicValue: number;
    audioValue: number;
}

class SettingModel extends MVC.BaseModel {
    private static _instance: SettingModel = null;
    
    public toggle: ToggleSettings = {
        music: true,
        audio: true,
        shake: true,
        friend: true,
        power: false,
        dub: true,
        hurtTips: true,
        musicValue: 1,
        audioValue: 1
    };
    
    private designSize: any = null;

    constructor() {
        super();
        if (SettingModel._instance == null) {
            SettingModel._instance = this;
        }
    }

    reset(): void {}

    initSetting(): void {
        const savedSettings = Manager.storage.getObject(StorageID.Setting_Data, {});
        Object.getOwnPropertyNames(this.toggle).forEach((key: string) => {
            if (savedSettings.hasOwnProperty(key)) {
                (this.toggle as any)[key] = savedSettings[key];
            }
        });

        if (!this.toggle.audio) {
            SettingModel.instance.toggle.audioValue = 0;
            Manager.audio.setEnableAudio(true);
        }

        if (this.toggle.music) {
            Notifier.send(ListenID.Setting_ValueMusic, SettingModel.instance.toggle.musicValue);
        } else {
            SettingModel.instance.toggle.musicValue = 0;
            Manager.audio.setMusicEnable(true, 0);
            Manager.audio.setMusicVolume(0);
        }
    }

    static get instance(): SettingModel {
        if (SettingModel._instance == null) {
            SettingModel._instance = new SettingModel();
        }
        return SettingModel._instance;
    }

    getRealDesignSize(): any {
        if (!this.designSize) {
            this.designSize = GameUtil.getRealDesignSize();
        }
        return this.designSize;
    }

    save(): void {
        Manager.storage.setObject(StorageID.Setting_Data, this.toggle);
    }
}

export default SettingModel;
