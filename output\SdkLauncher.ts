import { Cfg } from "Cfg";
import { Notifier } from "Notifier";
import { ListenID } from "ListenID";
import { Manager } from "Manager";
import { Time } from "Time";
import { AlertManager } from "AlertManager";
import { StorageID } from "StorageID";
import { GameUtil } from "GameUtil";
import { KnapsackVo } from "KnapsackVo";

export class SdkLauncher {
    public isLoadData: boolean = false;
    public finishWatch: any = null;
    public launchDesc: any = null;
    public pBar: any = null;
    private _pNum: number = 10;
    private _50state: boolean = false;

    constructor(launchDesc: any, progressBar: any) {
        this.launchDesc = launchDesc;
        this.pBar = progressBar;
        
        wonderSdk.setAlertAdpater(AlertManager);
        wonderSdk.setAudioAdapter(Manager.audio);
        Manager.net.setAppName(wonderSdk.BMS_APP_NAME);
        
        this._pNum = 10;
        this.showPrivacy();
        this.login();
    }

    async login(): Promise<void> {
        await Promise.all([
            this.loadTime(),
            this.loadGameSwitchConfig(),
            this.loadView()
        ]);

        const serverTime = new Date(Time.serverTimeMs);
        GameUtil.dateFormat2(serverTime, "yyyy-MM-dd HH:mm:ss.fff");
        
        await this.loadAllData();
        
        Notifier.send(ListenID.Login_Finish);
        console.log("[Login_Finish]");
    }

    async loadAllData(): Promise<void> {
        console.log("[loadAllData]");
        
        await this.sdkLogin();
        await this.requestSaveData();
        await this.loadUserData();
        await Promise.all([this.loadShareConfig()]);
        
        Manager.vo._knapsackVo = new KnapsackVo.Mgr(true, StorageID.MCatGame);
        this.initData();
        wonderSdk.preLoadRewardVideo();
    }

    initData(): void {
        const currentDay = new Date(Time.serverTimeMs).getDate();
        if (currentDay !== Manager.vo.userVo.day) {
            Manager.vo.userVo.day = currentDay;
            Manager.vo.userVo.loginDay++;
            Manager.vo.userVo.dailyData.freeCoin = true;
            Manager.vo.userVo.dailyData.freeDiamond = true;
            Notifier.send(ListenID.Game_SecondDay);
        }
        Manager.vo.isNewUser = Manager.vo.userVo.loginDay <= 1;
    }

    checkLogin(): void {
        if (this.isLoadData && Manager.vo.userVo.isAcceptPrivacy) {
            Time.doCancel(this.finishWatch);
            Notifier.send(ListenID.Login_Finish);
            this.finishWatch = null;
        }
    }

    async loadView(): Promise<void> {
        const viewPaths = [
            "ui/loading/LoadingView",
            "ui/ModeBackpackHero/M20_PrePare_Fight",
            "ui/main/MainView"
        ];
        
        const progressPerView = 0.45 / viewPaths.length;
        let loadedCount = 0;
        const totalCount = viewPaths.length;

        return new Promise<void>((resolve) => {
            if (viewPaths.length <= 0) {
                this.addProgress(0.4);
                resolve();
            } else {
                for (let i = 0; i < viewPaths.length; i++) {
                    const viewPath = viewPaths[i];
                    Manager.loader.loadRes(viewPath, cc.Prefab, 
                        (err: any, resource: any) => {
                            this.addProgress(progressPerView * (1 / resource));
                        },
                        () => {
                            loadedCount++;
                            Manager.storage.getNumber(StorageID.TempisFirst, 1);
                            if (loadedCount >= totalCount) {
                                resolve();
                            }
                        }
                    );
                }
            }
        });
    }

    async loadTime(): Promise<void> {
        return new Promise<void>((resolve) => {
            wonderSdk.requestServerTime().then((response: any) => {
                if (!wonderSdk.isTest && response && response.data.time) {
                    if (response && response.data.time) {
                        Time.setServerTime(1000 * response.data.time);
                    }
                } else {
                    const currentTime = Date.now();
                    Time.setServerTime(currentTime);
                }
                this.addProgress(0.03);
                resolve();
            }).catch(() => {
                const currentTime = Date.now();
                Time.setServerTime(currentTime);
                this.addProgress(0.03);
                resolve();
            });
        });
    }

    async loadMusic(): Promise<void> {
        const musicConfigs = Cfg.Sound.filter({ loop: true });
        const totalCount = musicConfigs.length;
        let loadedCount = 0;

        return new Promise<void>((resolve) => {
            const loadMusic = (index: number) => {
                const musicConfig = musicConfigs[index];
                const musicId = musicConfig.id;
                
                Manager.loader.loadRes(musicConfig.path, cc.AudioClip, (err: any, audioClip: cc.AudioClip) => {
                    loadedCount++;
                    if (!err) {
                        Manager.audio.setMusicClip(musicId, audioClip);
                    }
                    if (loadedCount >= totalCount) {
                        this.addProgress(0.1);
                        resolve();
                    }
                });
            };

            for (let i = 0; i < musicConfigs.length; i++) {
                loadMusic(i);
            }
        });
    }

    async loadUserData(): Promise<void> {
        return new Promise<void>((resolve) => {
            const userData = Manager.storage.getObject(StorageID.UserData, null);
            cc.log("[loadUserData]", userData);
            
            if (userData) {
                Manager.vo.userVo.updatetUserVo(userData);
            }
            
            if (!Manager.vo.userVo.openId) {
                Manager.vo.userVo.openId = Manager.vo.openId;
            }
            
            Manager.vo.isGetData = true;
            this.addProgress(0.1);
            resolve();
        });
    }

    async requestSaveData(): Promise<void> {
        return new Promise<void>((resolve) => {
            if (Manager.vo.openId) {
                const localUserData = Manager.storage.getObject(StorageID.UserData, null);
                
                wonderSdk.requestSaveData([StorageID.GameTag]).then((remoteData: string) => {
                    console.log("[requestSaveData]获取到远端数据Size:", remoteData.length);
                    
                    if (remoteData) {
                        const parsedData = JSON.parse(remoteData);
                        const remoteUserData = parsedData[StorageID.UserData];
                        
                        if (remoteUserData && (!localUserData || !localUserData.saveDataTime || 
                            remoteUserData.saveDataTime > localUserData.saveDataTime)) {
                            for (const key in parsedData) {
                                if (parsedData[key] && typeof parsedData[key] === "string") {
                                    Manager.storage.setString(key, parsedData[key]);
                                }
                            }
                        }
                    }
                    resolve();
                }).catch(() => {
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    async loadGameSwitchConfig(): Promise<void> {
        return new Promise<void>((resolve) => {
            wonderSdk.requestSwitchConfig().then((response: any) => {
                Manager.vo.updateSwitchVo(response.data);
                this.addProgress(0.12);
                resolve();
            }).catch(() => {
                this.addProgress(0.12);
                resolve();
            });
        });
    }

    async sdkLogin(): Promise<void> {
        return new Promise<void>((resolve) => {
            wonderSdk.login().then(() => {
                resolve();
            }).catch(() => {
                resolve();
            });
        });
    }

    async loadShareConfig(): Promise<void> {
        return new Promise<void>((resolve) => {
            wonderSdk.requestShareConfig().then(() => {
                resolve();
            }).catch(() => {
                resolve();
            });
        });
    }

    showPrivacy(): void {
        wonderSdk.showPrivacy((accepted: boolean) => {
            Manager.vo.userVo.isAcceptPrivacy = !!accepted;
        });
    }

    get pNum(): number {
        if (this._pNum > 100) {
            return 100;
        } else {
            return this._pNum;
        }
    }

    set pNum(value: number) {
        this._pNum = value;
        if (this._pNum > 100) {
            this._pNum = 100;
        }
    }

    addProgress(progress: number): void {
        if (this.pBar) {
            this.pNum = this.pNum + 100 * progress;
            this.pBar.progress = this.pNum / 100;
            
            if (this.pBar.progress >= 0.5 && !this._50state) {
                this._50state = true;
            }
            
            this.launchDesc.string = cc.js.formatStr("载入中...%d%", Math.round(this.pNum));
        }
    }
}
