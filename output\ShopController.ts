import { CallID } from "CallID";
import { ListenID } from "ListenID";
import { MVC } from "MVC";
import { Notifier } from "Notifier";
import { Time } from "Time";
import ShopModel from "ShopModel";

export class ShopController extends MVC.MController {
    
    constructor() {
        super();
        this.setup(ShopModel.instance);
        this.changeListener(true);
    }

    reset(): void {
        this._model.reset();
    }

    get classname(): string {
        return "ShopController";
    }

    registerAllProtocol(): void {}

    changeListener(enable: boolean): void {
        Notifier.changeListener(enable, ListenID.Login_Finish, this.onLoginFinish, this, 200);
        Notifier.changeListener(enable, ListenID.Shop_ShopItemList, this.onShop_ShopItemList, this);
        Notifier.changeCall(enable, CallID.Shop_GetInfo, this.getShopInfo, this);
        Notifier.changeCall(enable, CallID.Shop_GetItem, this.getShopItem, this);
    }

    onLoginFinish(): void {
        this.mode.loginFinish();
        this.getRiotShopInfo();
        Notifier.call(CallID.Shop_GetProductList);
        Time.delay(2, () => {
            Notifier.call(CallID.Shop_GetProductList);
        });
    }

    onShop_ShopItemList(itemList: any[]): void {
        this.mode.payList = itemList;
        this.mode.payList?.forEach((item: any) => {
            item.formPrice = item.formattedPrice;
        });
        Notifier.send(ListenID.Refresh_Item);
    }

    getMode(): any {
        return this._model;
    }

    getRiotShopInfo(): Promise<void> {
        return new Promise(() => {});
    }

    getShopInfo(): void {}

    getShopItem(): void {}

    onCharge(chargeInfo: any): void {
        this._model.onBuyChange(chargeInfo.id);
    }
}
